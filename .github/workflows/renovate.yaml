---
# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
name: Renovate

on:
  push:
    branches: ["main"]
    paths:
      - .renovaterc.json5
      - .renovate/**.json5
  schedule:
    - cron: "0 * * * *"
  workflow_dispatch:
    inputs:
      dryRun:
        description: Dry Run
        type: boolean
        default: false
        required: true
      logLevel:
        description: Log Level
        type: choice
        default: debug
        options:
          - debug
          - info
        required: true
      version:
        description: Renovate Version
        default: latest
        required: true

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  main:
    name: Renovate
    runs-on: ubuntu-latest
    permissions:
      packages: read
    steps:
      - name: Generate Token
        uses: actions/create-github-app-token@df432ceedc7162793a195dd1713ff69aefc7379e # v2.0.6
        id: app-token
        with:
          app-id: "${{ secrets.BOT_APP_ID }}"
          private-key: "${{ secrets.BOT_APP_PRIVATE_KEY }}"

      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false
          token: "${{ steps.app-token.outputs.token }}"

      - name: Run Renovate
        uses: renovatebot/github-action@a889a8abcb11ef7feaafaf5e483ea01d4bf7774e # v43.0.5
        env:
          LOG_LEVEL: "${{ inputs.logLevel || 'debug' }}"
          RENOVATE_AUTODISCOVER: true
          RENOVATE_AUTODISCOVER_FILTER: "${{ github.repository }}"
          RENOVATE_DRY_RUN: "${{ inputs.dryRun }}"
          RENOVATE_INTERNAL_CHECKS_FILTER: strict
          RENOVATE_PLATFORM: github
          RENOVATE_PLATFORM_COMMIT: true
        with:
          token: "${{ steps.app-token.outputs.token }}"
          renovate-version: "${{ inputs.version || 'latest' }}"
