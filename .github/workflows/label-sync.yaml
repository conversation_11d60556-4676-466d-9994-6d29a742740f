---
# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
name: Label Sync

on:
  workflow_dispatch:
  push:
    branches: ["main"]
    paths: [".github/labels.yaml"]
  schedule:
    - cron: "0 0 * * *" # Every day at midnight

permissions:
  contents: read

jobs:
  main:
    name: Label Sync - Sync Labels
    runs-on: ubuntu-latest
    permissions:
      contents: read
      issues: write
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false
          sparse-checkout: .github/labels.yaml

      - name: Sync Labels
        uses: EndBug/label-sync@52074158190acb45f3077f9099fea818aa43f97a # v2.3.3
        with:
          config-file: .github/labels.yaml
          delete-other-labels: true
