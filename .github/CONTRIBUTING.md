## Contribution guidelines

Welcome to [home-ops]! We're thrilled that you'd like to contribute. Your help is essential for making it better.

### Getting Started

Before you start contributing, please make sure you have read and understood our [Code of Conduct](CODE_OF_CONDUCT.md).

1. Fork the Repository

First, fork the [repository](https://github.com/varuntirumala1/home-ops) to your own GitHub account. This will create a copy of the project under your account.

2. Clone the Repository

    ```sh
    git clone https://github.com/varuntirumala1/home-ops
    ```

3. Navigate to the project directory 📁

    ```sh
    cd home-ops
    ```

4. Create a new branch for your feature or bug fix:

    ```sh
    git checkout -b feature-branch
    ```

5. Make your changes and commit them:

    ```sh
    git add .
    git commit -m "Description of your changes"
    ```

6. Push your changes to your fork:

    ```sh
    git push origin feature-branch
    ```

7. Finally Click on C<PERSON> Pull request to contribute on this repository.
