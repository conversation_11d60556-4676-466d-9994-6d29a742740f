---
# yaml-language-server: $schema=https://raw.githubusercontent.com/datreeio/CRDs-catalog/main/cert-manager.io/certificate_v1.json
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: prometheus-tls
  namespace: default
spec:
  secretName: prometheus-tls
  issuerRef:
    name: gts-production
    kind: ClusterIssuer
  privateKey:
    algorithm: ECDSA
    size: 384
  commonName: "prometheus.${SECRET_DOMAIN}"
  dnsNames:
    - "prometheus.${SECRET_DOMAIN}"
    - "alertmanager.${SECRET_DOMAIN}"
