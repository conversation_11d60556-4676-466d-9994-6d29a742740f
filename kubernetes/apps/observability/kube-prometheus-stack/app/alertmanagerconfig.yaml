---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/monitoring.coreos.com/alertmanagerconfig_v1alpha1.json
apiVersion: monitoring.coreos.com/v1alpha1
kind: AlertmanagerConfig
metadata:
  name: alertmanager
spec:
  route:
    groupBy: ["alertname", "job"]
    groupInterval: 10m
    groupWait: 1m
    receiver: pushover
    repeatInterval: 12h
    routes:
      - receiver: "null"
        matchers:
          - name: alertname
            value: InfoInhibitor
            matchType: =
      - receiver: heartbeat
        groupInterval: 5m
        groupWait: 0s
        repeatInterval: 5m
        matchers:
          - name: alertname
            value: Watchdog
            matchType: =
      - receiver: pushover
        matchers:
          - name: severity
            value: critical
            matchType: =
  inhibitRules:
    - equal: ["alertname", "namespace"]
      sourceMatch:
        - name: severity
          value: critical
          matchType: =
      targetMatch:
        - name: severity
          value: warning
          matchType: =
  receivers:
    - name: "null"
    - name: heartbeat
      webhookConfigs:
        - urlSecret:
            name: &secret alertmanager-secret
            key: ALERTMANAGER_HEARTBEAT_URL
    - name: pushover
      pushoverConfigs:
        - html: true
          message: |-
            {{- range .Alerts }}
              {{- if ne .Annotations.description "" }}
                {{ .Annotations.description }}
              {{- else if ne .Annotations.summary "" }}
                {{ .Annotations.summary }}
              {{- else if ne .Annotations.message "" }}
                {{ .Annotations.message }}
              {{- else }}
                Alert description not available
              {{- end }}
              {{- if gt (len .Labels.SortedPairs) 0 }}
                <small>
                  {{- range .Labels.SortedPairs }}
                    <b>{{ .Name }}:</b> {{ .Value }}
                  {{- end }}
                </small>
              {{- end }}
            {{- end }}
          priority: |-
            {{ if eq .Status "firing" }}1{{ else }}0{{ end }}
          sendResolved: true
          sound: gamelan
          title: >-
            [{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}]
            {{ .CommonLabels.alertname }}
          ttl: 86400s
          token:
            name: *secret
            key: ALERTMANAGER_PUSHOVER_TOKEN
          userKey:
            name: *secret
            key: PUSHOVER_USER_KEY
          urlTitle: View in Alertmanager
