---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/monitoring.coreos.com/scrapeconfig_v1alpha1.json
apiVersion: monitoring.coreos.com/v1alpha1
kind: ScrapeConfig
metadata:
  name: &name node-exporter
spec:
  staticConfigs:
    - targets:
        - nas01.${SECRET_DOMAIN}:9100
  metricsPath: /metrics
  relabelings:
    - action: replace
      targetLabel: job
      replacement: *name
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/monitoring.coreos.com/scrapeconfig_v1alpha1.json
apiVersion: monitoring.coreos.com/v1alpha1
kind: ScrapeConfig
metadata:
  name: &name smartctl-exporter
spec:
  staticConfigs:
    - targets:
        - nas01.${SECRET_DOMAIN}:9633
  metricsPath: /metrics
  relabelings:
    - action: replace
      targetLabel: job
      replacement: *name
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/monitoring.coreos.com/scrapeconfig_v1alpha1.json
apiVersion: monitoring.coreos.com/v1alpha1
kind: ScrapeConfig
metadata:
  name: &name onepassword-connect
spec:
  staticConfigs:
    - targets:
        - passwords.${SECRET_DOMAIN}
  metricsPath: /metrics
  relabelings:
    - action: replace
      targetLabel: job
      replacement: *name
