---
# yaml-language-server: $schema=https://json.schemastore.org/kustomization
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: observability
components:
  - ../../components/common
resources:
  - ./alloy/ks.yaml
  - ./blackbox-exporter/ks.yaml
  - ./grafana/ks.yaml
  - ./keda/ks.yaml
  - ./kromgo/ks.yaml
  - ./kube-prometheus-stack/ks.yaml
  - ./loki/ks.yaml
  - ./silence-operator/ks.yaml
