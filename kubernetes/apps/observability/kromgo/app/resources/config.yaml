---
# yaml-language-server: $schema=https://raw.githubusercontent.com/kashalls/kromgo/main/config.schema.json
badge:
  font: Verdana.ttf
  size: 12

metrics:
  - name: talos_version
    query: label_replace(node_os_info{name="Talos"}, "version_id", "$1", "version_id", "v(.+)")
    label: version_id
    title: Talos

  - name: kubernetes_version
    query: label_replace(kubernetes_build_info{service="kubernetes"}, "git_version", "$1", "git_version", "v(.+)")
    label: git_version
    title: Kubernetes

  - name: flux_version
    query: label_replace(flux_instance_info, "revision", "$1", "revision", "v(.+)@sha256:.+")
    label: revision
    title: Flux

  - name: cluster_node_count
    query: count(count by (node) (kube_node_status_condition{condition="Ready"}))
    colors:
      - { color: "green", min: 0, max: 9999 }
    title: Nodes

  - name: cluster_pod_count
    query: sum(kube_pod_status_phase{phase="Running"})
    colors:
      - { color: "green", min: 0, max: 9999 }
    title: Pods

  - name: cluster_cpu_usage
    query: round(avg(instance:node_cpu_utilisation:rate5m{kubernetes_node!=""}) * 100, 0.1)
    suffix: "%"
    colors:
      - { color: "green", min: 0, max: 35 }
      - { color: "orange", min: 36, max: 75 }
      - { color: "red", min: 76, max: 9999 }
    title: CPU

  - name: cluster_memory_usage
    query: round(sum(node_memory_MemTotal_bytes{kubernetes_node!=""} - node_memory_MemAvailable_bytes{kubernetes_node!=""}) / sum(node_memory_MemTotal_bytes{kubernetes_node!=""}) * 100, 0.1)
    suffix: "%"
    colors:
      - { color: green, min: 0, max: 35 }
      - { color: orange, min: 36, max: 75 }
      - { color: red, min: 76, max: 9999 }
    title: Memory

  - name: cluster_age_days
    query: round((time() - min(kube_node_created) ) / 86400)
    suffix: "d"
    colors:
      - { color: "green", min: 0, max: 180 }
      - { color: "orange", min: 181, max: 360 }
      - { color: "red", min: 361, max: 9999 }
    title: Age

  - name: cluster_uptime_days
    query: round(avg(node_time_seconds{kubernetes_node!=""} - node_boot_time_seconds{kubernetes_node!=""}) / 86400)
    suffix: "d"
    colors:
      - { color: "green", min: 0, max: 180 }
      - { color: "orange", min: 181, max: 360 }
      - { color: "red", min: 361, max: 9999 }
    title: Uptime

  - name: cluster_alert_count
    query: alertmanager_alerts{state="active"} - 1 # Ignore Watchdog
    colors:
      - { color: "green", min: 0, max: 0 }
      - { color: "red", min: 1, max: 9999 }
    title: Alerts
