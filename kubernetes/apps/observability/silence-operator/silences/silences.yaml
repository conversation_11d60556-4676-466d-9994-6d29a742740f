---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/observability.giantswarm.io/silence_v1alpha2.json
apiVersion: observability.giantswarm.io/v1alpha2
kind: Silence
metadata:
  name: ceph-node-nfsmount-diskspace-warning
spec:
  matchers:
    - name: alertname
      value: CephNodeDiskspaceWarning
    - name: mountpoint
      value: /etc/nfsmount.conf
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/observability.giantswarm.io/silence_v1alpha2.json
apiVersion: observability.giantswarm.io/v1alpha2
kind: Silence
metadata:
  name: ceph-node-local-diskspace-warning
spec:
  matchers:
    - name: alertname
      value: CephNodeDiskspaceWarning
    - name: device
      value: /dev/nvme.*
      matchType: "=~"
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/observability.giantswarm.io/silence_v1alpha2.json
apiVersion: observability.giantswarm.io/v1alpha2
kind: Silence
metadata:
  name: nas-memory-high-utilization
spec:
  matchers:
    - name: alertname
      value: NodeMemoryHighUtilization
    - name: instance
      value: nas01.${SECRET_DOMAIN}:9100
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/observability.giantswarm.io/silence_v1alpha2.json
apiVersion: observability.giantswarm.io/v1alpha2
kind: Silence
metadata:
  name: keda-hpa-maxed-out
spec:
  matchers:
    - name: alertname
      value: KubeHpaMaxedOut
    - name: horizontalpodautoscaler
      value: keda-hpa-.*
      matchType: "=~"
