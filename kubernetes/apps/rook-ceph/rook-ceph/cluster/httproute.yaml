---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/gateway.networking.k8s.io/httproute_v1.json
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: rook-ceph-dashboard
spec:
  hostnames: ["rook-ceph-cluster.${SECRET_DOMAIN}"]
  parentRefs:
    - name: rook-ceph-cluster-gateway
      namespace: rook-ceph
      sectionName: https
  rules:
    - backendRefs:
        - name: rook-ceph-mgr-dashboard
          namespace: rook-ceph
          port: 7000
