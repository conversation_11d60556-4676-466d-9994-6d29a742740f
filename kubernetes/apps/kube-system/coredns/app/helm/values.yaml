---
fullnameOverride: coredns
image:
  repository: mirror.gcr.io/coredns/coredns
replicaCount: 2
k8sAppLabelOverride: kube-dns
serviceAccount:
  create: true
service:
  name: kube-dns
  clusterIP: **********
servers:
  - zones:
      - zone: .
        scheme: dns://
        use_tcp: true
    port: 53
    plugins:
      - name: errors
      - name: health
        configBlock: |-
          lameduck 5s
      - name: ready
      - name: kubernetes
        parameters: cluster.local in-addr.arpa ip6.arpa
        configBlock: |-
          pods verified
          fallthrough in-addr.arpa ip6.arpa
      - name: autopath
        parameters: "@kubernetes"
      - name: forward
        parameters: . /etc/resolv.conf
      - name: cache
        configBlock: |-
          prefetch 20
          serve_stale
      - name: loop
      - name: reload
      - name: loadbalance
      - name: prometheus
        parameters: 0.0.0.0:9153
      - name: log
        configBlock: |-
          class error
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: node-role.kubernetes.io/control-plane
              operator: Exists
tolerations:
  - key: CriticalAddonsOnly
    operator: Exists
  - key: node-role.kubernetes.io/control-plane
    operator: Exists
    effect: NoSchedule
