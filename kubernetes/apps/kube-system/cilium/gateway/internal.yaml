---
# yaml-language-server: $schema=https://github.com/datreeio/CRDs-catalog/raw/refs/heads/main/gateway.networking.k8s.io/gateway_v1.json
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: internal
  annotations:
    external-dns.alpha.kubernetes.io/target: "internal.${SECRET_DOMAIN}"
spec:
  gatewayClassName: cilium
  addresses:
    - type: IPAddress
      value: "***************"
  infrastructure:
    annotations:
      external-dns.alpha.kubernetes.io/hostname: "internal.${SECRET_DOMAIN}"
  listeners:
    - name: http
      protocol: HTTP
      port: 80
      hostname: "*.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: All
    - name: https
      protocol: HTTPS
      port: 443
      hostname: "*.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: All
      tls:
        certificateRefs:
          - kind: Secret
            name: home-ops-wc-tls

