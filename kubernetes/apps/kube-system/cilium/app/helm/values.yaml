---
autoDirectNodeRoutes: true
bandwidthManager:
  enabled: true
  bbr: true
bpf:
  datapathMode: netkit
  masquerade: true
  preallocateMaps: true
  # tproxy: true
bpfClockProbe: true
bgpControlPlane:
  enabled: true
cgroup:
  automount:
    enabled: false
  hostRoot: /sys/fs/cgroup
cni:
  exclusive: false
dashboards:
  enabled: true
devices: ens+
enableIPv4BIGTCP: true
endpointRoutes:
  enabled: true
envoy:
  rollOutPods: true
  prometheus:
     serviceMonitor:
       enabled: true
gatewayAPI:
  enabled: true
  enableAlpn: true
  xffNumTrustedHops: 1
hubble:
  enabled: false
ipam:
  mode: kubernetes
ipv4NativeRoutingCIDR: *********/16
k8sServiceHost: 127.0.0.1
k8sServicePort: 7445
kubeProxyReplacement: true
kubeProxyReplacementHealthzBindAddr: 0.0.0.0:10256
l2announcements:
  enabled: true
loadBalancer:
  algorithm: maglev
  mode: dsr
localRedirectPolicy: true
operator:
  dashboards:
    enabled: true
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
  replicas: 2
  rollOutPods: true
prometheus:
  enabled: true
  serviceMonitor:
    enabled: true
    trustCRDsExist: true
rollOutCiliumPods: true
routingMode: native
securityContext:
  capabilities:
    ciliumAgent:
      - CHOWN
      - KILL
      - NET_ADMIN
      - NET_RAW
      - IPC_LOCK
      - SYS_ADMIN
      - SYS_RESOURCE
      - PERFMON
      - BPF
      - DAC_OVERRIDE
      - FOWNER
      - SETGID
      - SETUID
    cleanCiliumState:
      - NET_ADMIN
      - SYS_ADMIN
      - SYS_RESOURCE
