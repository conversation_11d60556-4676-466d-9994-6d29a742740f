---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/monitoring.coreos.com/prometheusrule_v1.json
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: volsync
spec:
  groups:
    - name: volsync.rules
      rules:
        - alert: VolSyncComponentAbsent
          expr: |
            absent(up{job="volsync-metrics"})
          annotations:
            summary: >-
              VolSync component has disappeared from Prometheus target discovery
          for: 5m
          labels:
            severity: critical

        - alert: VolSyncVolumeOutOfSync
          expr: |
            volsync_volume_out_of_sync == 1
          annotations:
            summary: >-
              {{ $labels.obj_namespace }}/{{ $labels.obj_name }} volume is out of sync
          for: 5m
          labels:
            severity: critical
