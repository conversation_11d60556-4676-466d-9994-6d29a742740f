---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/kustomize.toolkit.fluxcd.io/kustomization_v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: &app system-upgrade-controller
  namespace: &namespace system-upgrade
  labels:
    substitution.flux.home.arpa/disabled: "true"
spec:
  commonMetadata:
    labels:
      app.kubernetes.io/name: *app
  healthChecks:
    - apiVersion: helm.toolkit.fluxcd.io/v2
      kind: HelmRelease
      name: *app
      namespace: *namespace
  interval: 1h
  path: ./kubernetes/apps/system-upgrade/system-upgrade-controller/app
  prune: true
  retryInterval: 2m
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  targetNamespace: *namespace
  timeout: 5m
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/kustomize.toolkit.fluxcd.io/kustomization_v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: &app system-upgrade-controller-plans
  namespace: &namespace system-upgrade
  labels:
    substitution.flux.home.arpa/disabled: "true"
spec:
  commonMetadata:
    labels:
      app.kubernetes.io/name: *app
  dependsOn:
    - name: versions
      namespace: system-upgrade
    - name: system-upgrade-controller
      namespace: system-upgrade
  interval: 1h
  path: ./kubernetes/apps/system-upgrade/system-upgrade-controller/plans
  postBuild:
    substituteFrom:
      - kind: ConfigMap
        name: versions
  prune: true
  retryInterval: 2m
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  targetNamespace: *namespace
  timeout: 5m
  wait: false