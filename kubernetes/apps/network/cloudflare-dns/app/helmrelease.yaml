---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/source.toolkit.fluxcd.io/ocirepository_v1.json
apiVersion: source.toolkit.fluxcd.io/v1
kind: OCIRepository
metadata:
  name: cloudflare-dns
spec:
  interval: 5m
  layerSelector:
    mediaType: application/vnd.cncf.helm.chart.content.v1.tar+gzip
    operation: copy
  ref:
    tag: 1.18.0
  url: oci://ghcr.io/home-operations/charts-mirror/external-dns
---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/helm.toolkit.fluxcd.io/helmrelease_v2.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: &app cloudflare-dns
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: cloudflare-dns
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    fullnameOverride: *app
    provider:
      name: cloudflare
    env:
      - name: &name CF_API_TOKEN
        valueFrom:
          secretKeyRef:
            name: &secret cloudflare-dns-secret
            key: *name
      - name: &name CF_ZONE_ID
        valueFrom:
          secretKeyRef:
            name: *secret
            key: *name
    extraArgs:
      - --cloudflare-dns-records-per-page=1000
      - --cloudflare-proxied
      - --crd-source-apiversion=externaldns.k8s.io/v1alpha1
      - --crd-source-kind=DNSEndpoint
      - --gateway-name=external
      - --zone-id-filter=$(CF_ZONE_ID)
    triggerLoopOnEvent: true
    policy: sync
    sources: ["crd", "gateway-httproute"]
    txtOwnerId: default
    txtPrefix: k8s.
    domainFilters: ["${SECRET_DOMAIN}"]
    serviceMonitor:
      enabled: true
    podAnnotations:
      secret.reloader.stakater.com/reload: *secret
