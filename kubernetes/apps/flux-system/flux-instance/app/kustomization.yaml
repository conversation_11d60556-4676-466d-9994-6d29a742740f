---
# yaml-language-server: $schema=https://json.schemastore.org/kustomization
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ./externalsecret.yaml
  - ./helmrelease.yaml
  - ./httproute.yaml
  - ./prometheusrule.yaml
  - ./receiver.yaml
configMapGenerator:
  - name: flux-instance-values
    files:
      - values.yaml=./helm/values.yaml
configurations:
  - ./helm/kustomizeconfig.yaml
