---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/gateway.networking.k8s.io/httproute_v1.json
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: github-webhook
spec:
  hostnames: ["flux-webhook.${SECRET_DOMAIN}"]
  parentRefs:
    - name: external
      namespace: kube-system
      sectionName: https
  rules:
    - backendRefs:
        - name: webhook-receiver
          namespace: flux-system
          port: 80
      matches:
        - path:
            type: PathPrefix
            value: /hook/
