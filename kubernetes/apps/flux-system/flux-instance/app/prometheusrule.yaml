---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/monitoring.coreos.com/prometheusrule_v1.json
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: flux-instance-rules
  namespace: flux-system
spec:
  groups:
    - name: flux-instance.rules
      rules:
        - alert: FluxInstanceAbsent
          expr: |
            absent(flux_instance_info{exported_namespace="flux-system", name="flux"})
          for: 5m
          annotations:
            summary: >-
              Flux instance metric is missing
          labels:
            severity: critical

        - alert: FluxInstanceNotReady
          expr: |
            flux_instance_info{exported_namespace="flux-system", name="flux", ready!="True"}
          for: 5m
          annotations:
            summary: >-
              Flux instance {{ $labels.name }} is not ready
          labels:
            severity: critical
