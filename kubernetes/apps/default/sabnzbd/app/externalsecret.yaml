---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: sabnzbd
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: sabnzbd-secret
    template:
      data:
        SABNZBD__API_KEY: &apiKey "{{ .SABNZBD_API_KEY }}"
        SABNZBD__NZB_KEY: *apiKey
  dataFrom:
    - extract:
        key: sabnzbd
