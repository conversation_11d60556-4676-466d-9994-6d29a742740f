---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: sabnzbd
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      sabnzbd:
        annotations:
          reloader.stakater.com/auto: "true"
        containers:
          app:
            image:
              repository: ghcr.io/home-operations/sabnzbd
              tag: 4.5.2@sha256:e3f27e50ee51f950d89ce888cb3c3c4e74b46b42751333ee008f906906cbf05b
            env:
              SABNZBD__PORT: &port 80
              SABNZBD__HOST_WHITELIST_ENTRIES: >-
                sabnzbd,
                sabnzbd.default,
                sabnzbd.default.svc,
                sabnzbd.default.svc.cluster,
                sabnzbd.default.svc.cluster.local,
                sabnzbd.${SECRET_DOMAIN}
              TZ: America/New_York
            envFrom:
              - secretRef:
                  name: sabnzbd-secret
            probes:
              liveness: &probes
                enabled: true
                custom: true
                spec:
                  httpGet:
                    path: /api?mode=version
                    port: *port
                  initialDelaySeconds: 0
                  periodSeconds: 10
                  timeoutSeconds: 1
                  failureThreshold: 3
              readiness: *probes
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 100m
              limits:
                memory: 12Gi
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
    service:
      app:
        ports:
          http:
            port: *port
    route:
      app:
        hostnames:
          - "{{ .Release.Name }}.${SECRET_DOMAIN}"
        parentRefs:
          - name: sabnzbd-gateway
            namespace: default
            sectionName: https
    persistence:
      config:
        existingClaim: sabnzbd
      media:
        type: nfs
        server: nas01.${SECRET_DOMAIN}
        path: /mnt/flashstor/data
        globalMounts:
          - path: /media/usenet
            subPath: usenet
      tmpfs:
        type: emptyDir
        advancedMounts:
          sabnzbd:
            app:
              - path: /config/logs
                subPath: logs
              - path: /tmp
                subPath: tmp
