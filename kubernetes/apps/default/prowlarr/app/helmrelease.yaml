---
# yaml-language-server: $schema=https://raw.githubusercontent.com/bjw-s-labs/helm-charts/main/charts/other/app-template/schemas/helmrelease-helm-v2.schema.json
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: prowlarr
spec:
  interval: 1h
  chartRef:
    kind: OCIRepository
    name: app-template
  install:
    remediation:
      retries: -1
  upgrade:
    cleanupOnFail: true
    remediation:
      retries: 3
  values:
    controllers:
      prowlarr:
        annotations:
          reloader.stakater.com/auto: "true"
        containers:
          app:
            image:
              repository: ghcr.io/home-operations/prowlarr
              tag: 2.0.2.5116@sha256:30062fde00bf3901770d02e4e255b3fa1afa320ae2ff5359473ff7599be7ebc8
            env:
              PROWLARR__APP__INSTANCENAME: Prowlarr
              PROWLARR__APP__THEME: dark
              PROWLARR__AUTH__METHOD: Forms # use External to disable
              PROWLARR__AUTH__REQUIRED: Enabled #use DisabledForLocalAddresses to disable
              PROW<PERSON>RR__LOG__DBENABLED: "False"
              PROWLARR__LOG__LEVEL: info
              PROWLARR__SERVER__PORT: &port 80
              PROWLARR__UPDATE__BRANCH: develop
              TZ: America/New_York
            envFrom:
              - secretRef:
                  name: prowlarr-secret
            probes:
              liveness: &probes
                enabled: true
                custom: true
                spec:
                  httpGet:
                    path: /ping
                    port: *port
                  initialDelaySeconds: 0
                  periodSeconds: 10
                  timeoutSeconds: 1
                  failureThreshold: 3
              readiness: *probes
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities: { drop: ["ALL"] }
            resources:
              requests:
                cpu: 100m
              limits:
                memory: 1Gi
    defaultPodOptions:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        fsGroupChangePolicy: OnRootMismatch
    service:
      app:
        ports:
          http:
            port: *port
    route:
      app:
        hostnames: ["{{ .Release.Name }}.${SECRET_DOMAIN}"]
        parentRefs:
          - name: prowlarr-gateway
            namespace: default
            sectionName: https
    persistence:
      config:
        existingClaim: prowlarr
      tmp:
        type: emptyDir
