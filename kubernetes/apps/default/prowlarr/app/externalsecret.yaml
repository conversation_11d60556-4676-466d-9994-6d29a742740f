---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: prowlarr
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: prowlarr-secret
    template:
      data:
        PROWLARR__AUTH__APIKEY: "{{ .PROWLARR_API_KEY }}"
  dataFrom:
    - extract:
        key: prowlarr