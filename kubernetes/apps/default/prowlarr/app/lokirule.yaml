---
groups:
  - name: prowlarr
    rules:
      - alert: ProwlarrDatabaseIsLocked
        expr: |
          sum by (app) (count_over_time({app=~"prowlarr"} |~ "(?i)database is locked"[5m])) > 0
        for: 5m
        annotations:
          summary: >-
            {{ $labels.app }} is experiencing database issues
        labels:
          severity: critical

      - alert: ProwlarrDatabaseIsMalformed
        expr: |
          sum by (app) (count_over_time({app=~"prowlarr"} |~ "(?i)database disk image is malformed"[5m])) > 0
        for: 5m
        annotations:
          summary: >-
            {{ $labels.app }} is experiencing database issues
        labels:
          severity: critical
