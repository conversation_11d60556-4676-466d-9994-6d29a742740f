---
groups:
  - name: sonarr
    rules:
      - alert: SonarrDatabaseIsLocked
        expr: |
          sum by (app) (count_over_time({app=~"sonarr"} |~ "(?i)database is locked"[5m])) > 0
        for: 5m
        annotations:
          summary: >-
            {{ $labels.app }} is experiencing database issues
        labels:
          severity: critical

      - alert: SonarrDatabaseIsMalformed
        expr: |
          sum by (app) (count_over_time({app=~"sonarr"} |~ "(?i)database disk image is malformed"[5m])) > 0
        for: 5m
        annotations:
          summary: >-
            {{ $labels.app }} is experiencing database issues
        labels:
          severity: critical
