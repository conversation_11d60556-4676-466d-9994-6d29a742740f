---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: jellyseerr
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: jellyseerr-secret
    template:
      data:
        API_KEY: "{{ .JELLYSEERR_API_KEY }}"
  dataFrom:
    - extract:
        key: jellyseerr
