---
# yaml-language-server: $schema=https://raw.githubusercontent.com/datreeio/CRDs-catalog/main/cert-manager.io/certificate_v1.json
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: jellyseerr-tls
  namespace: default
spec:
  secretName: jellyseerr-tls
  issuerRef:
    name: gts-production
    kind: ClusterIssuer
  privateKey:
    algorithm: ECDSA
    size: 384
  commonName: "requests.${SECRET_DOMAIN}"
  dnsNames:
    - "requests.${SECRET_DOMAIN}"
    - "jellyseerr.${SECRET_DOMAIN}"
