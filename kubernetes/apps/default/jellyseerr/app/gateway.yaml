---
# yaml-language-server: $schema=https://github.com/datreeio/CRDs-catalog/raw/refs/heads/main/gateway.networking.k8s.io/gateway_v1.json
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: jellyseerr-gateway
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: gts-production
    cert-manager.io/private-key-algorithm: ECDSA
    cert-manager.io/private-key-size: "384"
spec:
  gatewayClassName: cilium
  addresses:
    - type: IPAddress
      value: ***************
  listeners:
    - name: http
      protocol: HTTP
      port: 80
      hostname: "requests.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
    - name: http-jellyseerr
      protocol: HTTP
      port: 80
      hostname: "jellyseerr.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
    - name: https
      protocol: HTTPS
      port: 443
      hostname: "requests.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: jellyseerr-tls
    - name: https-jellyseerr
      protocol: HTTPS
      port: 443
      hostname: "jellyseerr.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: jellyseerr-tls
