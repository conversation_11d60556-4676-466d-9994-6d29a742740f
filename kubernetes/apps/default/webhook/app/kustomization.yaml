---
# yaml-language-server: $schema=https://json.schemastore.org/kustomization
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
  - ./externalsecret.yaml
  - ./helmrelease.yaml
configMapGenerator:
  - name: webhook-configmap
    files:
      - hooks.yaml=./resources/hooks.yaml
      - jellyseerr-pushover.sh=./resources/jellyseerr-pushover.sh
      - radarr-pushover.sh=./resources/radarr-pushover.sh
      - sonarr-pushover.sh=./resources/sonarr-pushover.sh
      - sonarr-refresh-series.sh=./resources/sonarr-refresh-series.sh
      - sonarr-tag-codecs.sh=./resources/sonarr-tag-codecs.sh
generatorOptions:
  disableNameSuffixHash: true
  annotations:
    kustomize.toolkit.fluxcd.io/substitute: disabled
