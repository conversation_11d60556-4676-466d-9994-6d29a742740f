---
# yaml-language-server: $schema=https://raw.githubusercontent.com/datreeio/CRDs-catalog/main/cert-manager.io/certificate_v1.json
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: atuin-tls
  namespace: default
spec:
  secretName: atuin-tls
  issuerRef:
    name: gts-production
    kind: ClusterIssuer
  privateKey:
    algorithm: ECDSA
    size: 384
  commonName: "sh.${SECRET_DOMAIN}"
  dnsNames:
    - "sh.${SECRET_DOMAIN}"
    - "atuin.${SECRET_DOMAIN}"
