---
# yaml-language-server: $schema=https://github.com/datreeio/CRDs-catalog/raw/refs/heads/main/gateway.networking.k8s.io/gateway_v1.json
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: atuin-gateway
  namespace: default
  annotations:
    cert-manager.io/cluster-issuer: gts-production
    cert-manager.io/private-key-algorithm: ECDSA
    cert-manager.io/private-key-size: "384"
spec:
  gatewayClassName: cilium
  addresses:
    - type: IPAddress
      value: ***************
  listeners:
    - name: http
      protocol: HTTP
      port: 80
      hostname: "sh.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
    - name: http-atuin
      protocol: HTTP
      port: 80
      hostname: "atuin.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
    - name: https
      protocol: HTTPS
      port: 443
      hostname: "sh.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: atuin-tls
    - name: https-atuin
      protocol: HTTPS
      port: 443
      hostname: "atuin.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: atuin-tls
