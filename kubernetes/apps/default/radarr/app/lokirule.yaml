---
groups:
  - name: radarr
    rules:
      - alert: RadarrDatabaseIsLocked
        expr: |
          sum by (app) (count_over_time({app=~"radarr"} |~ "(?i)database is locked"[5m])) > 0
        for: 5m
        annotations:
          summary: >-
            {{ $labels.app }} is experiencing database issues
        labels:
          severity: critical

      - alert: RadarrDatabaseIsMalformed
        expr: |
          sum by (app) (count_over_time({app=~"radarr"} |~ "(?i)database disk image is malformed"[5m])) > 0
        for: 5m
        annotations:
          summary: >-
            {{ $labels.app }} is experiencing database issues
        labels:
          severity: critical
