---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: radarr
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: radarr-secret
    template:
      data:
        RADARR__AUTH__APIKEY: "{{ .RADARR_API_KEY }}"
  dataFrom:
    - extract:
        key: radarr
