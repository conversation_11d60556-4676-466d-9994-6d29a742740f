---
# yaml-language-server: $schema=https://raw.githubusercontent.com/datreeio/CRDs-catalog/main/cert-manager.io/certificate_v1.json
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: fusion-tls
  namespace: default
spec:
  secretName: fusion-tls
  issuerRef:
    name: gts-production
    kind: ClusterIssuer
  privateKey:
    algorithm: ECDSA
    size: 384
  commonName: "feeds.${SECRET_DOMAIN}"
  dnsNames:
    - "feeds.${SECRET_DOMAIN}"
    - "fusion.${SECRET_DOMAIN}"
