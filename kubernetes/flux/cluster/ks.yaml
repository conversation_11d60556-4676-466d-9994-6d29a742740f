---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/kustomize.toolkit.fluxcd.io/kustomization_v1.json
apiVersion: kustomize.toolkit.fluxcd.io/v1
kind: Kustomization
metadata:
  name: cluster-apps
  namespace: flux-system
spec:
  decryption:
    provider: sops
    secretRef:
      name: sops-age
  interval: 1h
  path: ./kubernetes/apps
  prune: true
  retryInterval: 2m
  sourceRef:
    kind: GitRepository
    name: flux-system
    namespace: flux-system
  timeout: 5m
  wait: false
  patches:
    - # Add Sops decryption to child Kustomizations
      patch: |-
        apiVersion: kustomize.toolkit.fluxcd.io/v1
        kind: Kustomization
        metadata:
          name: not-used
        spec:
          decryption:
            provider: sops
            secretRef:
              name: sops-age
      target:
        group: kustomize.toolkit.fluxcd.io
        kind: Kustomization
    - # Add common-config component to all child Kustomizations
      patch: |-
        apiVersion: kustomize.toolkit.fluxcd.io/v1
        kind: Kustomization
        metadata:
          name: not-used
        spec:
          postBuild:
            substituteFrom:
              - kind: Secret
                name: cluster-config-secret
                optional: false
      target:
        group: kustomize.toolkit.fluxcd.io
        kind: Kustomization
        labelSelector: "substitution.flux.home.arpa/disabled!=true"
