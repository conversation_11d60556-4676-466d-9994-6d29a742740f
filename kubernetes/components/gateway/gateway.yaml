---
# yaml-language-server: $schema=https://github.com/datreeio/CRDs-catalog/raw/refs/heads/main/gateway.networking.k8s.io/gateway_v1.json
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: ${APP}-gateway
  namespace: ${NAMESPACE}
  annotations:
    cert-manager.io/cluster-issuer: gts-production
    cert-manager.io/private-key-algorithm: ECDSA
    cert-manager.io/private-key-size: "384"
spec:
  gatewayClassName: cilium
  addresses:
    - type: IPAddress
      value: ${GATEWAY_IP}
  listeners:
    - name: http
      protocol: HTTP
      port: 80
      hostname: "${APP}.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
    - name: https
      protocol: HTTPS
      port: 443
      hostname: "${APP}.${SECRET_DOMAIN}"
      allowedRoutes:
        namespaces:
          from: Same
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: ${APP}-tls