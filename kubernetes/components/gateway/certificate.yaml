---
# yaml-language-server: $schema=https://raw.githubusercontent.com/datreeio/CRDs-catalog/main/cert-manager.io/certificate_v1.json
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: ${APP}-tls
  namespace: ${NAMESPACE}
spec:
  secretName: ${APP}-tls
  issuerRef:
    name: gts-production
    kind: ClusterIssuer
  privateKey:
    algorithm: ECDSA
    size: 384
  commonName: "${APP}.${SECRET_DOMAIN}"
  dnsNames:
    - "${APP}.${SECRET_DOMAIN}"