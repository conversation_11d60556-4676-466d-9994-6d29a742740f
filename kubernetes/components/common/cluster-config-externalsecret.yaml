---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: cluster-config
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: cluster-config-secret
    creationPolicy: Owner
    template:
      data:
        SECRET_DOMAIN: "{{ .SECRET_DOMAIN }}"
        EAB_KEY_ID: "{{ .EAB_KEY_ID }}"
        ACME_EMAIL: "{{ .ACME_EMAIL }}"
        CLOUDFLARE_TUNNEL_ID: "{{ .CLOUDFLARE_TUNNEL_ID }}"
  dataFrom:
    - extract:
        key: cluster-config
    - extract:
        key: cert-manager
    - extract:
        key: cloudflare
