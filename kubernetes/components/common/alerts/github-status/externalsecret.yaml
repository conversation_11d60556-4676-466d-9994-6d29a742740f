---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: github-status-token
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: github-status-token-secret
    template:
      data:
        token: "{{ .FLUX_GITHUB_TOKEN }}"
  dataFrom:
    - extract:
        key: flux
