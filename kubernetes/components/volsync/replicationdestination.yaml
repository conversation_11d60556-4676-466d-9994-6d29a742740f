---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/volsync.backube/replicationdestination_v1alpha1.json
apiVersion: volsync.backube/v1alpha1
kind: ReplicationDestination
metadata:
  name: "${APP}-dst"
  labels:
    kustomize.toolkit.fluxcd.io/ssa: IfNotPresent
spec:
  trigger:
    manual: restore-once
  restic:
    repository: "${APP}-volsync-secret"
    copyMethod: Snapshot
    volumeSnapshotClassName: "${VOLSYNC_SNAPSHOTCLASS:=csi-ceph-blockpool}"
    cacheStorageClassName: "${VOLSYNC_CACHE_SNAPSHOTCLASS:=openebs-hostpath}"
    cacheAccessModes: ["${VOLSYNC_CACHE_ACCESSMODES:=ReadWriteOnce}"]
    cacheCapacity: "${VOLSYNC_CACHE_CAPACITY:=5Gi}"
    storageClassName: "${VOLSYNC_STORAGECLASS:=ceph-block}"
    accessModes: ["${VOLSYNC_ACCESSMODES:=ReadWriteOnce}"]
    capacity: "${VOLSYNC_CAPACITY:=5Gi}"
    moverSecurityContext:
      runAsUser: ${VOLSYNC_PUID:=1000}
      runAsGroup: ${VOLSYNC_PGID:=1000}
      fsGroup: ${VOLSYNC_PGID:=1000}
    enableFileDeletion: true
    cleanupCachePVC: true
    cleanupTempPVC: true
