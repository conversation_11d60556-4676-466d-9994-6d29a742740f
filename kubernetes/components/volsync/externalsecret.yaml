---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/external-secrets.io/externalsecret_v1.json
apiVersion: external-secrets.io/v1
kind: ExternalSecret
metadata:
  name: "${APP}-volsync"
spec:
  secretStoreRef:
    kind: ClusterSecretStore
    name: onepassword
  target:
    name: "${APP}-volsync-secret"
    template:
      data:
        RESTIC_REPOSITORY: "/repository/${APP}"
        RESTIC_PASSWORD: "{{ .RESTIC_PASSWORD }}"
  dataFrom:
    - extract:
        key: volsync-template
