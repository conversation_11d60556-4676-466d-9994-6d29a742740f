{
  $schema: "https://docs.renovatebot.com/renovate-schema.json",
  packageRules: [
    {
      description: "Actions Runner Controller Group",
      groupName: "Actions Runner Controller",
      matchDatasources: ["docker"],
      matchPackageNames: ["/gha-runner-scale-set-controller/", "/gha-runner-scale-set/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
    },
    {
      description: "Cert-Manager Group",
      groupName: "Cert-Manager",
      matchDatasources: ["docker"],
      matchPackageNames: ["/cert-manager/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
    },
    {
      description: "Cilium Group",
      groupName: "Cilium",
      matchDatasources: ["docker"],
      matchPackageNames: ["/cilium/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
    },
    {
      description: "CoreDNS Group",
      groupName: "CoreDNS",
      matchDatasources: ["docker"],
      matchPackageNames: ["/coredns/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
    },
    {
      description: "External Secrets Operator Group",
      groupName: "External Secrets Operator",
      matchDatasources: ["docker"],
      matchPackageNames: ["/external-secrets/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
    },
    {
      description: "Flux Operator Group",
      groupName: "Flux Operator",
      matchDatasources: ["docker"],
      matchPackageNames: ["/flux-operator/", "/flux-instance/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
    },
    {
      description: "Intel Device Plugins Group",
      groupName: "Intel-Device-Plugins",
      matchDatasources: ["docker"],
      matchPackageNames: ["/intel-device-plugins-operator/", "/intel-device-plugins-gpu/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
    },
    {
      description: "Rook-Ceph Group",
      groupName: "Rook-Ceph",
      matchDatasources: ["docker"],
      matchPackageNames: ["/rook-ceph/", "/rook-ceph-cluster/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
    },
    {
      description: "Spegel Group",
      groupName: "Spegel",
      matchDatasources: ["docker"],
      matchPackageNames: ["/spegel/"],
      group: {
        commitMessageTopic: "{{{groupName}}} group",
      },
    },
  ],
}
