{
  $schema: "https://docs.renovatebot.com/renovate-schema.json",
  packageRules: [
    {
      description: "Auto-merge trusted container digests",
      matchDatasources: ["docker"],
      automerge: true,
      automergeType: "pr",
      matchUpdateTypes: ["digest"],
      matchPackageNames: ["/home-operations/"],
      ignoreTests: false,
    },
    {
      description: "Auto-merge OCI Charts",
      matchDatasources: ["docker"],
      automerge: true,
      automergeType: "pr",
      matchUpdateTypes: ["minor", "patch"],
      matchPackageNames: ["/kube-prometheus-stack/", "/grafana/"],
      ignoreTests: false,
    },
    {
      description: "Auto-merge GitHub Actions",
      matchManagers: ["github-actions"],
      automerge: true,
      automergeType: "branch",
      matchUpdateTypes: ["minor", "patch", "digest"],
      minimumReleaseAge: "3 days",
      ignoreTests: true,
    },
    {
      description: "Auto-merge trusted GitHub Actions",
      matchManagers: ["github-actions"],
      matchPackageNames: [
        "/^actions\//",
        "/^renovatebot\//",
      ],
      automerge: true,
      automergeType: "branch",
      matchUpdateTypes: ["minor", "patch", "digest"],
      minimumReleaseAge: "1 minute",
      ignoreTests: true,
    },
    {
      description: "Auto-merge GitHub Releases",
      matchDatasources: ["github-releases"],
      automerge: true,
      automergeType: "branch",
      matchUpdateTypes: ["minor", "patch"],
      matchPackageNames: ["/external-dns/", "/gateway-api/", "/prometheus-operator/"],
      ignoreTests: true,
    },
  ],
}
