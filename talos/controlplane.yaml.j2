---
version: v1alpha1
debug: false
persist: true
machine:
  token: op://Infrastructure/talos/MACHINE_TOKEN
  ca:
    crt: op://Infrastructure/talos/MACHINE_CA_CRT
    key: op://Infrastructure/talos/MACHINE_CA_KEY
  certSANs: ["127.0.0.1", "***************"]
  kubelet:
    image: ghcr.io/siderolabs/kubelet:{{ ENV.KUBERNETES_VERSION }}
    extraConfig:
      serializeImagePulls: false
    extraMounts:
      - destination: /var/mnt/extra
        type: bind
        source: /var/mnt/extra
        options: ["bind", "rshared", "rw"]
    defaultRuntimeSeccompProfileEnabled: true
    nodeIP:
      validSubnets: ["*************/22"]
    disableManifestsDirectory: true
  network:
    interfaces:
      - deviceSelector:
          physical: true
        dhcp: true
        vip:
          ip: ***************
  install:
    disk: /dev/vda
    extraKernelArgs:
      - -init_on_alloc                      # Less security, faster puter
      - -init_on_free                       # Less security, faster puter
      - -selinux                            # Less security, faster puter
      - apparmor=0                          # Less security, faster puter
      - init_on_alloc=0                     # Less security, faster puter
      - init_on_free=0                      # Less security, faster puter
      - mitigations=off                     # Less security, faster puter
      - security=none                       # Less security, faster puter
      - talos.auditd.disabled=1             # Less security, faster puter
    wipe: false
  files:
    - # Spegel
      op: create
      path: /etc/cri/conf.d/20-customization.part
      content: |
        [plugins."io.containerd.cri.v1.images"]
          discard_unpacked_layers = false
    - op: overwrite
      path: /etc/nfsmount.conf
      permissions: 0o644
      content: |
        [ NFSMount_Global_Options ]
        nfsvers=4.2
        hard=True
        nconnect=16
        noatime=True
  time:
    disabled: false
    servers:
      - *************
      - ***************
      - ***********
  sysctls:
    fs.inotify.max_user_watches: 1048576   # Watchdog
    fs.inotify.max_user_instances: 8192    # Watchdog
    net.core.default_qdisc: fq             # 10Gb/s
    net.core.rmem_max: 67108864            # 10Gb/s | Cloudflared / QUIC
    net.core.wmem_max: 67108864            # 10Gb/s | Cloudflared / QUIC
    net.ipv4.tcp_congestion_control: bbr   # 10Gb/s
    net.ipv4.tcp_fastopen: 3               # Send and accept data in the opening SYN packet
    net.ipv4.tcp_mtu_probing: 1            # 10Gb/s | Jumbo frames
    net.ipv4.tcp_rmem: 4096 87380 33554432 # 10Gb/s
    net.ipv4.tcp_wmem: 4096 65536 33554432 # 10Gb/s
    net.ipv4.tcp_window_scaling: 1         # 10Gb/s
    sunrpc.tcp_slot_table_entries: 128     # 10Gb/s | NFS
    sunrpc.tcp_max_slot_table_entries: 128 # 10Gb/s | NFS
    user.max_user_namespaces: 11255        # User Namespaces
    vm.nr_hugepages: 1024                  # PostgreSQL
  features:
    rbac: true
    stableHostname: true
    kubernetesTalosAPIAccess:
      enabled: true
      allowedRoles: ["os:admin"]
      allowedKubernetesNamespaces: ["actions-runner-system", "system-upgrade"]
    apidCheckExtKeyUsage: true
    diskQuotaSupport: true
    kubePrism:
      enabled: true
      port: 7445
    hostDNS:
      enabled: true
      resolveMemberNames: true
      forwardKubeDNSToHost: false
  nodeLabels:
    intel.feature.node.kubernetes.io/gpu: "true"
    topology.kubernetes.io/region: main
    topology.kubernetes.io/zone: m
cluster:
  id: op://Infrastructure/talos/CLUSTER_ID
  secret: op://Infrastructure/talos/CLUSTER_SECRET
  controlPlane:
    endpoint: https://***************:6443
  clusterName: main
  network:
    cni:
      name: none
    dnsDomain: cluster.local
    podSubnets: ["*********/16"]
    serviceSubnets: ["*********/16"]
  coreDNS:
    disabled: true
  token: op://Infrastructure/talos/CLUSTER_TOKEN
  secretboxEncryptionSecret: op://Infrastructure/talos/CLUSTER_SECRETBOXENCRYPTIONSECRET
  ca:
    crt: op://Infrastructure/talos/CLUSTER_CA_CRT
    key: op://Infrastructure/talos/CLUSTER_CA_KEY
  aggregatorCA:
    crt: op://Infrastructure/talos/CLUSTER_AGGREGATORCA_CRT
    key: op://Infrastructure/talos/CLUSTER_AGGREGATORCA_KEY
  serviceAccount:
    key: op://Infrastructure/talos/CLUSTER_SERVICEACCOUNT_KEY
  apiServer:
    image: registry.k8s.io/kube-apiserver:{{ ENV.KUBERNETES_VERSION }}
    extraArgs:
      enable-aggregator-routing: true
      feature-gates: MutatingAdmissionPolicy=true
      runtime-config: admissionregistration.k8s.io/v1alpha1=true
    certSANs: ["127.0.0.1", "***************"]
    disablePodSecurityPolicy: true
    auditPolicy:
      apiVersion: audit.k8s.io/v1
      kind: Policy
      rules:
        - level: Metadata
  controllerManager:
    image: registry.k8s.io/kube-controller-manager:{{ ENV.KUBERNETES_VERSION }}
    extraArgs:
      bind-address: 0.0.0.0
  proxy:
    disabled: true
    image: registry.k8s.io/kube-proxy:{{ ENV.KUBERNETES_VERSION }}
  scheduler:
    image: registry.k8s.io/kube-scheduler:{{ ENV.KUBERNETES_VERSION }}
    extraArgs:
      bind-address: 0.0.0.0
    config:
      apiVersion: kubescheduler.config.k8s.io/v1
      kind: KubeSchedulerConfiguration
      profiles:
        - schedulerName: default-scheduler
          plugins:
            score:
              disabled:
                - name: ImageLocality
          pluginConfig:
            - name: PodTopologySpread
              args:
                defaultingType: List
                defaultConstraints:
                  - maxSkew: 1
                    topologyKey: kubernetes.io/hostname
                    whenUnsatisfiable: ScheduleAnyway
  etcd:
    ca:
      crt: op://Infrastructure/talos/CLUSTER_ETCD_CA_CRT
      key: op://Infrastructure/talos/CLUSTER_ETCD_CA_KEY
    extraArgs:
      listen-metrics-urls: http://0.0.0.0:2381
    advertisedSubnets: ["*************/22"]
  allowSchedulingOnControlPlanes: true
