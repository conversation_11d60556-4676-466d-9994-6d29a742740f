---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

vars:
  WORKSTATION_RESOURCES_DIR: '{{.ROOT_DIR}}/.taskfiles/workstation/resources'

tasks:

  brew:
    desc: Set up Homebrew tools
    cmds:
      - brew bundle --file {{.WORKSTATION_RESOURCES_DIR}}/Brewfile
    sources:
      - '{{.WORKSTATION_RESOURCES_DIR}}/Brewfile'
    generates:
      - '{{.WORKSTATION_RESOURCES_DIR}}/Brewfile.lock.json'
    preconditions:
      - which brew
      - test -f {{.WORKSTATION_RESOURCES_DIR}}/Brewfile

  krew:
    desc: Set up Krew tools
    deps: [brew]
    cmds:
      - kubectl krew install cert-manager cnpg browse-pvc node-shell rook-ceph view-secret
    preconditions:
      - kubectl krew version
      - which kubectl
