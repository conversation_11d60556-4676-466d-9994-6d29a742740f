---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

tasks:

  browse-pvc:
    desc: Mount a PVC to an temp container [NS=default] [CLAIM=required]
    interactive: true
    cmd: kubectl browse-pvc --namespace {{.NS}} --image docker.io/library/alpine:latest {{.CLAIM}}
    vars:
      NS: '{{.NS | default "default"}}'
    requires:
      vars: [CLAIM]
    preconditions:
      - kubectl --namespace {{.NS}} get persistentvolumeclaims {{.CLAIM}}
      - kubectl browse-pvc --version
      - which kubectl

  node-shell:
    desc: Open a shell to a node [NODE=required]
    interactive: true
    cmd: kubectl node-shell -n kube-system -x {{.NODE}}
    requires:
      vars: [NODE]
    preconditions:
      - kubectl get nodes {{.NODE}}
      - kubectl node-shell --version
      - which kubectl

  sync-secrets:
    desc: Sync all ExternalSecrets
    cmds:
      - for: { var: SECRETS, split: "\n" }
        cmd: kubectl --namespace {{splitList "," .ITEM | first}} annotate externalsecret {{splitList "," .ITEM | last}} force-sync="{{now | unixEpoch}}" --overwrite
    vars:
      SECRETS:
        sh: kubectl get externalsecret --all-namespaces --no-headers --output=jsonpath='{range .items[*]}{.metadata.namespace},{.metadata.name}{"\n"}{end}'
    preconditions:
      - which kubectl

  cleanse-pods:
    desc: Cleanse pods with a Failed/Pending/Succeeded phase
    cmds:
      - for:
          matrix:
            PHASE: [Failed, Pending, Succeeded]
        cmd: kubectl delete pods --all-namespaces --field-selector status.phase={{.ITEM.PHASE}} --ignore-not-found=true
    preconditions:
      - which kubectl

  # https://docs.github.com/en/enterprise-cloud@latest/actions/hosting-your-own-runners/managing-self-hosted-runners-with-actions-runner-controller/deploying-runner-scale-sets-with-actions-runner-controller#upgrading-arc
  upgrade-arc:
    desc: Upgrade the ARC
    cmds:
      - helm -n actions-runner-system uninstall home-ops-runner
      - helm -n actions-runner-system uninstall actions-runner-controller
      - sleep 5
      - flux -n actions-runner-system reconcile hr actions-runner-controller
      - flux -n actions-runner-system reconcile hr home-ops-runner
    preconditions:
      - which flux helm
