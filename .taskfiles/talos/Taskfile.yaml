---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

tasks:

  apply-node:
    desc: Apply <PERSON><PERSON> config to a node [IP=required] [MODE=auto]
    cmd: |
      bash {{.SCRIPTS_DIR}}/render-machine-config.sh {{.TALOS_DIR}}/{{.MACHINE_TYPE}}.yaml.j2 {{.TALOS_DIR}}/nodes/{{.IP}}.yaml.j2 \
        | talosctl --nodes {{.IP}} apply-config --mode {{.MODE}} --file /dev/stdin
    vars:
      MODE: '{{.MODE | default "auto"}}'
      MACHINE_TYPE:
        sh: talosctl --nodes {{.IP}} get machinetypes --output=jsonpath='{.spec}'
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - test -f {{.SCRIPTS_DIR}}/render-machine-config.sh
      - which talosctl

  upgrade-node:
    desc: Upgrade Talos on a single node [IP=required] [MODE=powercycle]
    cmd: talosctl --nodes {{.IP}} upgrade --image="{{.FACTORY_IMAGE}}" --reboot-mode={{.MODE}} --timeout=10m
    vars:
      MODE: '{{.MODE | default "powercycle"}}'
      FACTORY_IMAGE:
        sh: minijinja-cli {{.TALOS_DIR}}/nodes/{{.IP}}.yaml.j2 | yq --exit-status '.machine.install.image'
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - test -f {{.TALOS_DIR}}/nodes/{{.IP}}.yaml.j2
      - which minijinja-cli talosctl yq

  upgrade-k8s:
    desc: Upgrade Kubernetes across the whole cluster
    cmd: talosctl --nodes {{.NODE}} upgrade-k8s --to $KUBERNETES_VERSION
    vars:
      NODE:
        sh: talosctl config info --output json | jq --exit-status --raw-output '.endpoints[]' | shuf -n 1
    preconditions:
      - talosctl --nodes {{.NODE}} get machineconfig
      - which jq talosctl

  reboot-node:
    desc: Reboot Talos on a single node [IP=required] [MODE=powercycle]
    cmd: talosctl --nodes {{.IP}} reboot --mode={{.MODE}}
    vars:
      MODE: '{{.MODE | default "powercycle"}}'
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - which talosctl

  shutdown-cluster:
    desc: Shutdown Talos across the whole cluster
    prompt: Shutdown the Talos cluster ... continue?
    cmd: talosctl shutdown --nodes {{.NODES}} --force
    vars:
      NODES:
        sh: talosctl config info --output json | jq --exit-status --join-output '[.nodes[]] | join(",")'
    preconditions:
      - talosctl --nodes {{.NODES}} get machineconfig
      - which jq talosctl

  reset-node:
    desc: Reset Talos on a single node [IP=required]
    prompt: Reset Talos node '{{.IP}}' ... continue?
    cmd: talosctl reset --nodes {{.IP}} --graceful=false
    requires:
      vars: [IP]
    preconditions:
      - talosctl --nodes {{.IP}} get machineconfig
      - which talosctl

  reset-cluster:
    desc: Reset Talos across the whole cluster
    prompt: Reset the Talos cluster ... continue?
    cmd: talosctl reset --nodes {{.NODES}} --graceful=false
    vars:
      NODES:
        sh: talosctl config info --output json | jq --exit-status --join-output '[.nodes[]] | join(",")'
    preconditions:
      - talosctl --nodes {{.NODES}} get machineconfig
      - which jq talosctl

  kubeconfig:
    desc: Generate the kubeconfig for a Talos cluster
    cmd: talosctl kubeconfig --nodes {{.NODE}} --force --force-context-name main {{.ROOT_DIR}}
    vars:
      NODE:
        sh: talosctl config info --output json | jq --exit-status --raw-output '.endpoints[]' | shuf -n 1
    preconditions:
      - talosctl --nodes {{.NODE}} get machineconfig
      - which jq talosctl
