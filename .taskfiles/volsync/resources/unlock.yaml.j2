---
apiVersion: batch/v1
kind: Job
metadata:
  name: volsync-unlock-{{ ENV.APP }}
  namespace: {{ ENV.NS }}
spec:
  ttlSecondsAfterFinished: 3600
  template:
    spec:
      automountServiceAccountToken: false
      restartPolicy: OnFailure
      containers:
        - name: nfs
          image: docker.io/restic/restic:latest
          args: ["unlock", "--remove-all"]
          envFrom:
            - secretRef:
                name: {{ ENV.APP }}-volsync-secret
          volumeMounts:
            - name: repository
              mountPath: /repository
          resources: {}
      volumes:
        - name: repository
          nfs:
            server: 192.168.120.10
            path: /mnt/flashstor/Volsync
