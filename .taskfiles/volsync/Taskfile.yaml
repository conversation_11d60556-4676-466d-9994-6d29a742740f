---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

# Taskfile used to manage certain VolSync tasks for a given application, limitations are as followed.
#   1. Fluxtomization, HelmRelease, PVC, ReplicationSource all have the same name (e.g. plex)
#   2. ReplicationSource and ReplicationDestination are a Restic repository
#   3. Each application only has one PVC that is being replicated

vars:
  VOLSYNC_RESOURCES_DIR: '{{.ROOT_DIR}}/.taskfiles/volsync/resources'

tasks:

  state-*:
    desc: Suspend or resume Volsync
    cmds:
      - flux --namespace volsync-system {{.STATE}} kustomization volsync
      - flux --namespace volsync-system {{.STATE}} helmrelease volsync
      - kubectl --namespace volsync-system scale deployment volsync --replicas {{if eq .STATE "suspend"}}0{{else}}1{{end}}
    vars:
      STATE: '{{index .MATCH 0}}'
    preconditions:
      - '[[ "{{.STATE}}" == "suspend" || "{{.STATE}}" == "resume" ]]'
      - which flux kubectl

  snapshot:
    desc: Snapshot an app [NS=default] [APP=required]
    cmds:
      - kubectl --namespace {{.NS}} patch replicationsources {{.APP}} --type merge -p '{"spec":{"trigger":{"manual":"{{now | unixEpoch}}"}}}'
      - until kubectl --namespace {{.NS}} get job/{{.JOB}} &>/dev/null; do sleep 5; done
      - kubectl --namespace {{.NS}} wait job/{{.JOB}} --for=condition=complete --timeout=120m
    vars:
      NS: '{{.NS | default "default"}}'
      JOB: volsync-src-{{.APP}}
    requires:
      vars: [APP]
    preconditions:
      - kubectl --namespace {{.NS}} get replicationsources {{.APP}}
      - which kubectl

  restore:
    desc: Restore an app [NS=default] [APP=required] [PREVIOUS=required]
    cmds:
      # Suspend
      - flux --namespace {{.NS}} suspend kustomization {{.APP}}
      - flux --namespace {{.NS}} suspend helmrelease {{.APP}}
      - kubectl --namespace {{.NS}} scale {{.CONTROLLER}}/{{.APP}} --replicas 0
      - kubectl --namespace {{.NS}} wait pod --for=delete --selector="app.kubernetes.io/name={{.APP}}" --timeout=5m
      # Restore
      - minijinja-cli {{.VOLSYNC_RESOURCES_DIR}}/replicationdestination.yaml.j2 | kubectl apply --server-side --filename -
      - until kubectl --namespace {{.NS}} get job/volsync-dst-{{.APP}}-manual &>/dev/null; do sleep 5; done
      - kubectl --namespace {{.NS}} wait job/volsync-dst-{{.APP}}-manual --for=condition=complete --timeout=120m
      - kubectl --namespace {{.NS}} delete replicationdestination {{.APP}}-manual
      # Resume
      - flux --namespace {{.NS}} resume kustomization {{.APP}}
      - flux --namespace {{.NS}} resume helmrelease {{.APP}}
      - flux --namespace {{.NS}} reconcile helmrelease {{.APP}} --force
      - kubectl --namespace {{.NS}} wait pod --for=condition=ready --selector="app.kubernetes.io/name={{.APP}}" --timeout=5m
    vars:
      NS: '{{.NS | default "default"}}'
      CONTROLLER:
        sh: kubectl --namespace {{.NS}} get deployment {{.APP}} &>/dev/null && echo deployment || echo statefulset
    env:
      NS: '{{.NS}}'
      APP: '{{.APP}}'
      PREVIOUS: '{{.PREVIOUS}}'
      CLAIM:
        sh: kubectl --namespace {{.NS}} get replicationsources/{{.APP}} --output=jsonpath='{.spec.sourcePVC}'
      ACCESS_MODES:
        sh: kubectl --namespace {{.NS}} get replicationsources/{{.APP}} --output=jsonpath='{.spec.restic.accessModes}'
      STORAGE_CLASS_NAME:
        sh: kubectl --namespace {{.NS}} get replicationsources/{{.APP}} --output=jsonpath='{.spec.restic.storageClassName}'
      PUID:
        sh: kubectl --namespace {{.NS}} get replicationsources/{{.APP}} --output=jsonpath='{.spec.restic.moverSecurityContext.runAsUser}'
      PGID:
        sh: kubectl --namespace {{.NS}} get replicationsources/{{.APP}} --output=jsonpath='{.spec.restic.moverSecurityContext.runAsGroup}'
    requires:
      vars: [APP, PREVIOUS]
    preconditions:
      - test -f {{.VOLSYNC_RESOURCES_DIR}}/replicationdestination.yaml.j2
      - which flux kubectl minijinja-cli

  unlock:
    desc: Unlock a restic source repo from local machine [NS=default] [APP=required]
    cmds:
      - minijinja-cli {{.VOLSYNC_RESOURCES_DIR}}/unlock.yaml.j2 | kubectl apply --server-side --filename -
      - until kubectl --namespace {{.NS}} get job/volsync-unlock-{{.APP}} &>/dev/null; do sleep 5; done
      - kubectl --namespace {{.NS}} wait job/volsync-unlock-{{.APP}} --for condition=complete --timeout=5m
      - stern --namespace {{.NS}} job/volsync-unlock-{{.APP}} --no-follow
      - kubectl --namespace {{.NS}} delete job volsync-unlock-{{.APP}}
    vars:
      NS: '{{.NS | default "default"}}'
    env:
      NS: '{{.NS}}'
      APP: '{{.APP}}'
    requires:
      vars: [APP]
    preconditions:
      - test -f {{.VOLSYNC_RESOURCES_DIR}}/unlock.yaml.j2
      - which kubectl minijinja-cli stern
