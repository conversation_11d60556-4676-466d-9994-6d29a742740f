{
  $schema: "https://docs.renovatebot.com/renovate-schema.json",
  extends: [
    "config:recommended",
    "docker:enableMajor",
    "helpers:pinGitHubActionDigests",
    "github>varuntirumala1/home-ops//.renovate/autoMerge.json5",
    "github>varuntirumala1/home-ops//.renovate/customManagers.json5",
    "github>varuntirumala1/home-ops//.renovate/grafanaDashboards.json5",
    "github>varuntirumala1/home-ops//.renovate/groups.json5",
    "github>varuntirumala1/home-ops//.renovate/labels.json5",
    "github>varuntirumala1/home-ops//.renovate/semanticCommits.json5",
    ":automergeBranch",
    ":disableRateLimiting",
    ":dependencyDashboard",
    ":semanticCommits",
    ":timezone(America/New_York)",
  ],
  dependencyDashboardTitle: "Renovate Dashboard 🤖",
  suppressNotifications: [
    "prEditedNotification",
    "prIgnoreNotification",
  ],
  ignorePaths: [
    "**/*.sops.*",
    "**/resources/**",
  ],
  flux: {
    managerFilePatterns: ["/(^|/)kubernetes/.+\\.ya?ml$/"]
  },
  "helm-values": {
    managerFilePatterns: ["/(^|/)kubernetes/.+\\.ya?ml$/"]
  },
  kubernetes: {
    managerFilePatterns: ["/(^|/)kubernetes/.+\\.ya?ml$/"]
  },
}
