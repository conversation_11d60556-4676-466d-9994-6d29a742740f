---
# yaml-language-server: $schema=https://taskfile.dev/schema.json
version: '3'

set: [pipefail]
shopt: [globstar]

vars:
  BOOTSTRAP_DIR: '{{.ROOT_DIR}}/bootstrap'
  KUBERNETES_DIR: '{{.ROOT_DIR}}/kubernetes'
  SCRIPTS_DIR: '{{.ROOT_DIR}}/scripts'
  TALOS_DIR: '{{.ROOT_DIR}}/talos'

dotenv:
  - '{{.ROOT_DIR}}/onepassword.env'
  - '{{.KUBERNETES_DIR}}/apps/system-upgrade/versions/versions.env'

env:
  KUBECONFIG: '{{.ROOT_DIR}}/kubeconfig'
  MINIJINJA_CONFIG_FILE: '{{.ROOT_DIR}}/.minijinja.toml'
  SOPS_AGE_KEY_FILE: '{{.ROOT_DIR}}/age.key'
  TALOSCONFIG: '{{.ROOT_DIR}}/talosconfig'

includes:
  bootstrap: .taskfiles/bootstrap
  kubernetes: .taskfiles/kubernetes
  talos: .taskfiles/talos
  volsync: .taskfiles/volsync
  workstation: .taskfiles/workstation

tasks:

  default:
    cmd: task --list
    silent: true
