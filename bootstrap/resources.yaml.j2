{% for namespace in ["external-secrets", "flux-system", "network"] %}
---
apiVersion: v1
kind: Namespace
metadata:
  name: {{ namespace }}
{% endfor %}
---
apiVersion: v1
kind: Secret
metadata:
  name: onepassword-secret
  namespace: external-secrets
stringData:
  token: op://Infrastructure/1password/OP_CONNECT_TOKEN
---
apiVersion: v1
kind: Secret
metadata:
  name: sops-age
  namespace: flux-system
stringData:
  age.agekey: op://Infrastructure/sops/SOPS_PRIVATE_KEY
---
apiVersion: v1
kind: Secret
metadata:
  name: cloudflare-tunnel-id-secret
  namespace: network
stringData:
  CLOUDFLARE_TUNNEL_ID: op://Infrastructure/cloudflare/CLOUDFLARE_TUNNEL_ID
